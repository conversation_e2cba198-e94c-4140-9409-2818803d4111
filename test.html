<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态表格测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .pages-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 20px;
            background: #f0f0f0;
        }

        .page {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            background: #f5f5f5;
            border-bottom: 2px solid #ddd;
        }

        .title {
            text-align: center;
            margin: 10px 0;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .header-row {
            display: flex;
            background: #e8e8e8;
            font-weight: bold;
            border-bottom: 1px solid #ccc;
        }

        .header-cell {
            padding: 8px 10px;
            border-right: 1px solid #ccc;
            text-align: center;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .header-cell:last-child {
            border-right: none;
        }

        .table-content {
            flex: 1;
            overflow: hidden;
        }

        .table-row {
            display: flex;
            border-bottom: 1px solid #eee;
            align-items: stretch;
        }

        .table-row:hover {
            background-color: #f9f9f9;
        }

        .table-cell {
            border-right: 1px solid #eee;
            display: flex;
            align-items: center;
            padding: 0;
        }

        .table-cell:last-child {
            border-right: none;
        }

        .cell-content {
            padding: 8px 10px;
            font-size: 12px;
            line-height: 1.4;
            word-wrap: break-word;
            word-break: break-all;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div id="app">
        <dynamic-table
            :data="tableData"
            :columns="tableColumns"
            :title="tableTitle"
            :container-width="700"
            :container-height="1000"
        ></dynamic-table>
    </div>

    <script>
        const { createApp, ref, computed, onMounted, nextTick, watch } = Vue;

        // 动态表格组件
        const DynamicTable = {
            props: {
                data: { type: Array, required: true },
                columns: { type: Array, required: true },
                title: { type: String, default: '数据表格' },
                containerWidth: { type: Number, default: 700 },
                containerHeight: { type: Number, default: 1000 }
            },
            setup(props) {
                const rowHeights = ref([]);
                const allPages = ref([]);

                const pageStyle = computed(() => ({
                    width: props.containerWidth + 'px',
                    height: props.containerHeight + 'px'
                }));

                const availableContentHeight = computed(() => {
                    return props.containerHeight - 80;
                });

                const getRowHeight = (pageIndex, rowIndex) => {
                    const globalRowIndex = allPages.value.slice(0, pageIndex)
                        .reduce((sum, page) => sum + page.length, 0) + rowIndex;
                    return rowHeights.value[globalRowIndex] || 40;
                };

                let measureCanvas = null;
                const getMeasureCanvas = () => {
                    if (!measureCanvas) {
                        measureCanvas = document.createElement('canvas');
                    }
                    return measureCanvas;
                };

                const measureTextHeight = (text, width, fontSize = 12, fontFamily = 'Arial, sans-serif') => {
                    if (!text || width <= 0) return 40;
                    
                    const canvas = getMeasureCanvas();
                    const context = canvas.getContext('2d');
                    context.font = `${fontSize}px ${fontFamily}`;
                    
                    const padding = 20;
                    const availableWidth = width - padding;
                    const lineHeight = fontSize * 1.4;
                    
                    const chars = Array.from(text);
                    const lines = [];
                    let currentLine = '';
                    
                    for (const char of chars) {
                        const testLine = currentLine + char;
                        const metrics = context.measureText(testLine);
                        
                        if (metrics.width > availableWidth && currentLine.length > 0) {
                            lines.push(currentLine);
                            currentLine = char;
                        } else {
                            currentLine = testLine;
                        }
                    }
                    
                    if (currentLine) {
                        lines.push(currentLine);
                    }
                    
                    const totalHeight = Math.max(lines.length * lineHeight + 16, 40);
                    return Math.ceil(totalHeight);
                };

                const calculateRowHeights = async () => {
                    await nextTick();
                    
                    const heights = [];
                    
                    for (let i = 0; i < props.data.length; i++) {
                        const row = props.data[i];
                        let maxHeight = 40;
                        
                        props.columns.forEach(column => {
                            const text = String(row[column.key] || '');
                            const cellHeight = measureTextHeight(text, column.width, 12);
                            maxHeight = Math.max(maxHeight, cellHeight);
                        });
                        
                        heights.push(maxHeight);
                    }
                    
                    rowHeights.value = heights;
                    splitDataIntoPages();
                };

                const splitDataIntoPages = () => {
                    const pages = [];
                    let currentPageData = [];
                    let currentPageHeight = 0;
                    const maxPageHeight = availableContentHeight.value;
                    
                    for (let i = 0; i < props.data.length; i++) {
                        const rowHeight = rowHeights.value[i];
                        
                        if (currentPageHeight + rowHeight > maxPageHeight && currentPageData.length > 0) {
                            pages.push([...currentPageData]);
                            currentPageData = [props.data[i]];
                            currentPageHeight = rowHeight;
                        } else {
                            currentPageData.push(props.data[i]);
                            currentPageHeight += rowHeight;
                        }
                    }
                    
                    if (currentPageData.length > 0) {
                        pages.push(currentPageData);
                    }
                    
                    allPages.value = pages;
                };

                watch(() => props.data, () => {
                    if (props.data.length > 0) {
                        calculateRowHeights();
                    }
                }, { immediate: true });

                onMounted(() => {
                    if (props.data.length > 0) {
                        calculateRowHeights();
                    }
                });

                return {
                    allPages,
                    pageStyle,
                    getRowHeight
                };
            },
            template: `
                <div class="pages-container">
                    <div 
                        v-for="(pageData, pageIndex) in allPages" 
                        :key="pageIndex"
                        class="page"
                        :style="pageStyle"
                    >
                        <div class="table-header">
                            <h2 class="title">{{ title }}{{ allPages.length > 1 ? \` (第\${pageIndex + 1}页)\` : '' }}</h2>
                            <div class="header-row">
                                <div 
                                    v-for="(column, index) in columns" 
                                    :key="index"
                                    class="header-cell"
                                    :style="{ width: column.width + 'px' }"
                                >
                                    {{ column.label }}
                                </div>
                            </div>
                        </div>
                        <div class="table-content">
                            <div 
                                v-for="(row, rowIndex) in pageData" 
                                :key="row.sequence || rowIndex"
                                class="table-row"
                                :style="{ height: getRowHeight(pageIndex, rowIndex) + 'px' }"
                            >
                                <div 
                                    v-for="(column, colIndex) in columns"
                                    :key="colIndex"
                                    class="table-cell"
                                    :style="{ width: column.width + 'px' }"
                                >
                                    <div class="cell-content">
                                        {{ row[column.key] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `
        };

        // 创建应用
        createApp({
            components: {
                DynamicTable
            },
            setup() {
                const tableTitle = ref('表4.2 上海大学Nature和Science论文清单');
                
                const tableColumns = ref([
                    { key: 'sequence', label: '序号', width: 50 },
                    { key: 'title', label: '论文名称', width: 200 },
                    { key: 'journal', label: '期刊', width: 80 },
                    { key: 'year', label: '发表年份', width: 80 },
                    { key: 'volume', label: '卷/期', width: 80 },
                    { key: 'impact', label: '影响因子', width: 70 },
                    { key: 'partition', label: '分区', width: 50 },
                    { key: 'authors', label: '作者', width: 90 }
                ]);

                const tableData = ref([
                    {
                        sequence: 1,
                        title: 'Fabrication of red-emitting carbon dots with enhanced fluorescence for optical anti-counterfeiting and information encryption',
                        journal: 'NATURE',
                        year: 2024,
                        volume: '631/8019',
                        impact: 1,
                        partition: 1,
                        authors: '张三等'
                    },
                    {
                        sequence: 2,
                        title: 'Nature of metal-support interaction for metal nanoparticles on oxide supports',
                        journal: 'SCIENCE',
                        year: 2024,
                        volume: '384/6724',
                        impact: 0.25,
                        partition: 0.25,
                        authors: '李四等'
                    }
                    // 可以添加更多测试数据
                ]);

                // 添加更多测试数据
                for (let i = 3; i <= 30; i++) {
                    tableData.value.push({
                        sequence: i,
                        title: `这是第${i}篇论文的标题，内容可能会很长很长很长很长很长很长很长很长很长很长很长很长，需要测试动态高度计算功能，确保在固定页面尺寸内正确显示所有内容`,
                        journal: i % 2 === 0 ? 'NATURE' : 'SCIENCE',
                        year: 2020 + (i % 5),
                        volume: `${600 + i}/${7000 + i}`,
                        impact: (Math.random() * 0.9 + 0.1).toFixed(2),
                        partition: (Math.random() * 0.9 + 0.1).toFixed(2),
                        authors: `作者${i}等`
                    });
                }

                return {
                    tableTitle,
                    tableColumns,
                    tableData
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
