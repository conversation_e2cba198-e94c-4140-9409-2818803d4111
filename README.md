# Hit Server - Node.js Tracking/Analytics API

A lightweight Node.js API server for tracking and analytics data with SQLite database storage.

## Features

- **POST /dpa** - Main tracking endpoint for analytics data
- **GET /dpa** - Retrieve all tracking events (for testing/debugging)
- **GET /health** - Health check endpoint
- SQLite database for data persistence
- Comprehensive input validation
- Error handling and logging
- CORS support
- Graceful shutdown handling

## API Specification

### POST /dpa

Accepts JSON payload with the following structure:

```json
{
    "userId": 34189126,
    "univCode": "RC00116", 
    "location": "http://************:3800/pd-dpa-overall",
    "seqNo": 28,
    "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
    "event": "export_data",
    "args": {"currVerNo": 202505}
}
```

**Field Validation:**
- `userId` (number, required): Must be a positive integer
- `univCode` (string, required): Cannot be empty
- `location` (string, required): Must be a valid URL
- `seqNo` (number, required): Must be non-negative
- `session` (string, required): Must be a valid UUID format
- `event` (string, optional): Cannot be empty if provided
- `args` (object, optional): Will be stored as JSON string if provided

**Success Response (201):**
```json
{
    "success": true,
    "code": 200,
    "message": "Tracking event recorded successfully",
    "data": {
        "id": 1,
        "userId": 34189126,
        "univCode": "RC00116",
        "location": "http://************:3800/pd-dpa-overall",
        "seqNo": 28,
        "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
        "event": "export_data",
        "args": {"currVerNo": 202505},
        "timestamp": "2025-07-14 11:09:36"
    }
}
```

**Success Response without optional fields (201):**
```json
{
    "success": true,
    "code": 200,
    "message": "Tracking event recorded successfully",
    "data": {
        "id": 2,
        "userId": 12345,
        "univCode": "TEST001",
        "location": "http://example.com/test",
        "seqNo": 1,
        "session": "550e8400-e29b-41d4-a716-************",
        "event": null,
        "args": null,
        "timestamp": "2025-07-14 11:09:36"
    }
}
```

**Error Response (400):**
```json
{
    "error": "Validation failed",
    "details": {
        "missingFields": ["userId"],
        "invalidTypes": [
            {
                "field": "seqNo",
                "expected": "number",
                "actual": "string"
            }
        ]
    }
}
```

## Installation & Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start the server:**
   ```bash
   npm start
   ```
   
   Or for development:
   ```bash
   npm run dev
   ```

3. **Server will start on:**
   - URL: http://localhost:3000
   - DPA endpoint: http://localhost:3000/dpa
   - Health check: http://localhost:3000/health

## Database

The application uses SQLite with the following schema:

```sql
CREATE TABLE tracking_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    userId INTEGER NOT NULL,
    univCode TEXT NOT NULL,
    location TEXT NOT NULL,
    seqNo INTEGER NOT NULL,
    session TEXT NOT NULL,
    event TEXT,
    args TEXT,
    created_at DATETIME DEFAULT (datetime('now', 'localtime'))
);
```

Database file: `tracking.db` (created automatically)

**时间说明:**
- 所有时间戳使用服务器本地时间
- 数据库中的 `created_at` 字段自动设置为服务器当前本地时间
- API 响应中的 `timestamp` 字段也使用服务器本地时间格式
- 时间格式: `YYYY-MM-DD HH:MM:SS`

## Testing

Run the test suite to verify all endpoints work correctly:

```bash
node test-api.js
```

The test suite includes:
- Health check test
- Valid DPA request test
- Valid DPA request without optional fields test
- Missing field validation tests
- Invalid data type tests
- URL format validation
- UUID format validation
- Retrieve events test

## Data Management

### Clear Tracking Data

Use the interactive data cleaning tool to manage your tracking data:

```bash
# Using Node.js directly
node clear-data.js

# Using npm script
npm run clear-data

# On Windows using batch file
clear-data.bat

# View data statistics only (demo mode)
npm run demo-clear
```

**Available options:**
1. **Clear all data** - Remove all tracking events (keeps table structure)
2. **Clear by User ID** - Remove data for specific user
3. **Clear by time range** - Remove data within specified date/time range
4. **Delete database file** - Completely remove the database file
5. **View statistics** - Show data statistics and summaries
6. **Exit** - Close the tool

**Data Statistics includes:**
- Total record count
- Records per user (top 10)
- Records per event type (top 10)
- Time range of data (earliest to latest)

**Safety features:**
- Confirmation prompts for destructive operations
- Data count preview before deletion
- Graceful error handling
- Resource cleanup on exit

## Project Structure

```
hit-server/
├── index.js          # Main server file
├── database.js       # Database operations
├── routes.js         # API routes
├── validation.js     # Input validation middleware
├── test-api.js       # Test suite
├── clear-data.js     # Data cleaning tool
├── clear-data.bat    # Windows batch file for data cleaning
├── test-curl.bat     # Windows curl test script
├── package.json      # Dependencies and scripts
├── README.md         # This file
└── tracking.db       # SQLite database (created automatically)
```

## Example Usage

### Using curl:

```bash
# Health check
curl http://localhost:3000/health

# Send tracking data
curl -X POST http://localhost:3000/dpa \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 34189126,
    "univCode": "RC00116",
    "location": "http://************:3800/pd-dpa-overall",
    "seqNo": 28,
    "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
    "event": "export_data",
    "args": {"currVerNo": 202505}
  }'

# Get all tracking events
curl http://localhost:3000/dpa

# Send tracking data without optional fields
curl -X POST http://localhost:3000/dpa \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 12345,
    "univCode": "TEST001",
    "location": "http://example.com/test",
    "seqNo": 1,
    "session": "550e8400-e29b-41d4-a716-************"
  }'
```

### Using JavaScript fetch:

```javascript
const response = await fetch('http://localhost:3000/dpa', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        userId: 34189126,
        univCode: "RC00116",
        location: "http://************:3800/pd-dpa-overall",
        seqNo: 28,
        session: "fbf89896-eeb5-4586-921b-b2a636b826e9",
        event: "export_data",
        args: {currVerNo: 202505}
    })
});

const result = await response.json();
console.log(result);
```

## Environment Variables

- `PORT` - Server port (default: 3000)
- `HOST` - Server host (default: localhost)
- `NODE_ENV` - Environment mode (development/production)

## Error Handling

The API includes comprehensive error handling for:
- Invalid JSON payloads
- Missing required fields
- Invalid data types
- URL format validation
- UUID format validation
- Database errors
- Server errors

## Graceful Shutdown

The server supports graceful shutdown on SIGTERM and SIGINT signals:
- Stops accepting new connections
- Closes database connections
- Exits cleanly

Press `Ctrl+C` to stop the server gracefully.
