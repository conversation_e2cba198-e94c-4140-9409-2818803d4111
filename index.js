const express = require('express');
const cors = require('cors');
const Database = require('./database');
const createRoutes = require('./routes');

// Configuration
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

// Create Express application
const app = express();

// Database instance
let database;

/**
 * Configure middleware
 */
function setupMiddleware() {
    // Enable CORS for all routes
    app.use(cors({
        origin: true, // Allow all origins for development
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization'],
        credentials: true
    }));

    // Parse JSON bodies (with size limit)
    app.use(express.json({ 
        limit: '10mb',
        strict: true
    }));

    // Parse URL-encoded bodies
    app.use(express.urlencoded({ 
        extended: true,
        limit: '10mb'
    }));

    // Request logging middleware
    app.use((req, res, next) => {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);
        next();
    });
}

/**
 * Setup error handling middleware
 */
function setupErrorHandling() {
    // Handle 404 errors
    app.use((req, res) => {
        res.status(404).json({
            success: false,
            error: 'Not Found',
            message: `Route ${req.method} ${req.url} not found`
        });
    });

    // Global error handler
    app.use((err, req, res, next) => {
        console.error('Unhandled error:', err);

        // Handle JSON parsing errors
        if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
            return res.status(400).json({
                success: false,
                error: 'Invalid JSON',
                message: 'Request body contains invalid JSON'
            });
        }

        // Handle other errors
        res.status(err.status || 500).json({
            success: false,
            error: 'Internal Server Error',
            message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
        });
    });
}

/**
 * Initialize the database and start the server
 */
async function startServer() {
    try {
        // Initialize database
        console.log('Initializing database...');
        database = new Database();
        await database.init();

        // Setup middleware
        setupMiddleware();

        // Setup routes
        const routes = createRoutes(database);
        app.use('/', routes);

        // Setup error handling
        setupErrorHandling();

        // Start the server
        const server = app.listen(PORT, HOST, () => {
            console.log(`🚀 Hit Server is running on http://${HOST}:${PORT}`);
            console.log(`📊 DPA endpoint available at: http://${HOST}:${PORT}/dpa`);
            console.log(`❤️  Health check available at: http://${HOST}:${PORT}/health`);
            console.log('Press Ctrl+C to stop the server');
        });

        // Graceful shutdown handling
        process.on('SIGTERM', () => gracefulShutdown(server));
        process.on('SIGINT', () => gracefulShutdown(server));

    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

/**
 * Graceful shutdown handler
 */
async function gracefulShutdown(server) {
    console.log('\n🛑 Received shutdown signal. Shutting down gracefully...');
    
    // Stop accepting new connections
    server.close(async () => {
        console.log('HTTP server closed');
        
        // Close database connection
        if (database) {
            try {
                await database.close();
                console.log('Database connection closed');
            } catch (error) {
                console.error('Error closing database:', error);
            }
        }
        
        console.log('Shutdown complete');
        process.exit(0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
        console.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
    }, 10000);
}

// Start the server
if (require.main === module) {
    startServer();
}

module.exports = app;
