/**
 * 清空打点数据脚本
 * 提供多种清空数据的选项：
 * 1. 清空所有数据
 * 2. 按条件清空数据
 * 3. 删除数据库文件
 */

const Database = require('./database');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class DataCleaner {
    constructor() {
        this.database = null;
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * 询问用户确认
     */
    async askConfirmation(message) {
        return new Promise((resolve) => {
            this.rl.question(`${message} (y/N): `, (answer) => {
                resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
            });
        });
    }

    /**
     * 显示菜单选项
     */
    showMenu() {
        console.log('\n📊 打点数据清理工具');
        console.log('==================');
        console.log('1. 清空所有打点数据 (保留表结构)');
        console.log('2. 按用户ID清空数据');
        console.log('3. 按时间范围清空数据');
        console.log('4. 删除整个数据库文件');
        console.log('5. 查看数据统计');
        console.log('6. 退出');
        console.log('');
    }

    /**
     * 获取用户选择
     */
    async getUserChoice() {
        return new Promise((resolve) => {
            this.rl.question('请选择操作 (1-6): ', (answer) => {
                resolve(parseInt(answer));
            });
        });
    }

    /**
     * 获取用户输入
     */
    async getUserInput(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * 初始化数据库连接
     */
    async initDatabase() {
        try {
            this.database = new Database();
            await this.database.init();
            console.log('✅ 数据库连接成功');
        } catch (error) {
            console.error('❌ 数据库连接失败:', error.message);
            throw error;
        }
    }

    /**
     * 清空所有数据
     */
    async clearAllData() {
        try {
            const confirmed = await this.askConfirmation('⚠️  确定要清空所有打点数据吗？此操作不可恢复！');
            if (!confirmed) {
                console.log('❌ 操作已取消');
                return;
            }

            // 获取清空前的数据统计
            const beforeCount = await this.getDataCount();
            console.log(`📊 清空前共有 ${beforeCount} 条记录`);

            // 执行清空操作
            await this.executeSQL('DELETE FROM tracking_events');
            
            // 重置自增ID
            await this.executeSQL('DELETE FROM sqlite_sequence WHERE name="tracking_events"');
            
            console.log('✅ 所有打点数据已清空');
            console.log('✅ 自增ID已重置');
            
        } catch (error) {
            console.error('❌ 清空数据失败:', error.message);
        }
    }

    /**
     * 按用户ID清空数据
     */
    async clearDataByUserId() {
        try {
            const userId = await this.getUserInput('请输入要清空的用户ID: ');
            if (!userId || isNaN(userId)) {
                console.log('❌ 无效的用户ID');
                return;
            }

            // 查询该用户的数据数量
            const count = await this.getDataCountByUserId(parseInt(userId));
            if (count === 0) {
                console.log(`📊 用户ID ${userId} 没有数据`);
                return;
            }

            const confirmed = await this.askConfirmation(`⚠️  确定要清空用户ID ${userId} 的 ${count} 条数据吗？`);
            if (!confirmed) {
                console.log('❌ 操作已取消');
                return;
            }

            // 执行删除操作
            await this.executeSQL('DELETE FROM tracking_events WHERE userId = ?', [parseInt(userId)]);
            console.log(`✅ 用户ID ${userId} 的 ${count} 条数据已清空`);
            
        } catch (error) {
            console.error('❌ 清空数据失败:', error.message);
        }
    }

    /**
     * 按时间范围清空数据
     */
    async clearDataByTimeRange() {
        try {
            console.log('请输入时间范围 (格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD)');
            const startTime = await this.getUserInput('开始时间: ');
            const endTime = await this.getUserInput('结束时间: ');

            if (!startTime || !endTime) {
                console.log('❌ 时间范围不能为空');
                return;
            }

            // 查询该时间范围的数据数量
            const count = await this.getDataCountByTimeRange(startTime, endTime);
            if (count === 0) {
                console.log(`📊 时间范围 ${startTime} 到 ${endTime} 没有数据`);
                return;
            }

            const confirmed = await this.askConfirmation(`⚠️  确定要清空 ${startTime} 到 ${endTime} 的 ${count} 条数据吗？`);
            if (!confirmed) {
                console.log('❌ 操作已取消');
                return;
            }

            // 执行删除操作
            await this.executeSQL(
                'DELETE FROM tracking_events WHERE created_at BETWEEN ? AND ?', 
                [startTime, endTime]
            );
            console.log(`✅ 时间范围 ${startTime} 到 ${endTime} 的 ${count} 条数据已清空`);
            
        } catch (error) {
            console.error('❌ 清空数据失败:', error.message);
        }
    }

    /**
     * 删除数据库文件
     */
    async deleteDatabaseFile() {
        try {
            const dbPath = path.join(__dirname, 'tracking.db');
            
            if (!fs.existsSync(dbPath)) {
                console.log('📊 数据库文件不存在');
                return;
            }

            const confirmed = await this.askConfirmation('⚠️  确定要删除整个数据库文件吗？此操作不可恢复！');
            if (!confirmed) {
                console.log('❌ 操作已取消');
                return;
            }

            // 关闭数据库连接
            if (this.database) {
                await this.database.close();
                this.database = null;
            }

            // 删除文件
            fs.unlinkSync(dbPath);
            console.log('✅ 数据库文件已删除');
            console.log('💡 下次启动服务器时将创建新的空数据库');
            
        } catch (error) {
            console.error('❌ 删除数据库文件失败:', error.message);
        }
    }

    /**
     * 查看数据统计
     */
    async showDataStatistics() {
        try {
            const totalCount = await this.getDataCount();
            console.log('\n📊 数据统计信息');
            console.log('================');
            console.log(`总记录数: ${totalCount}`);

            if (totalCount > 0) {
                // 按用户统计
                const userStats = await this.getUserStatistics();
                console.log('\n👥 用户统计:');
                userStats.forEach(stat => {
                    console.log(`  用户ID ${stat.userId}: ${stat.count} 条记录`);
                });

                // 按事件类型统计
                const eventStats = await this.getEventStatistics();
                console.log('\n📝 事件统计:');
                eventStats.forEach(stat => {
                    const eventName = stat.event || '(无事件)';
                    console.log(`  ${eventName}: ${stat.count} 条记录`);
                });

                // 时间范围
                const timeRange = await this.getTimeRange();
                if (timeRange.earliest && timeRange.latest) {
                    console.log('\n⏰ 时间范围:');
                    console.log(`  最早记录: ${timeRange.earliest}`);
                    console.log(`  最新记录: ${timeRange.latest}`);
                }
            }
            
        } catch (error) {
            console.error('❌ 获取统计信息失败:', error.message);
        }
    }

    /**
     * 执行SQL语句
     */
    async executeSQL(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.database.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(this.changes);
            });
        });
    }

    /**
     * 获取数据总数
     */
    async getDataCount() {
        return new Promise((resolve, reject) => {
            this.database.db.get('SELECT COUNT(*) as count FROM tracking_events', [], (err, row) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(row.count);
            });
        });
    }

    /**
     * 按用户ID获取数据数量
     */
    async getDataCountByUserId(userId) {
        return new Promise((resolve, reject) => {
            this.database.db.get(
                'SELECT COUNT(*) as count FROM tracking_events WHERE userId = ?', 
                [userId], 
                (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(row.count);
                }
            );
        });
    }

    /**
     * 按时间范围获取数据数量
     */
    async getDataCountByTimeRange(startTime, endTime) {
        return new Promise((resolve, reject) => {
            this.database.db.get(
                'SELECT COUNT(*) as count FROM tracking_events WHERE created_at BETWEEN ? AND ?', 
                [startTime, endTime], 
                (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(row.count);
                }
            );
        });
    }

    /**
     * 获取用户统计
     */
    async getUserStatistics() {
        return new Promise((resolve, reject) => {
            this.database.db.all(
                'SELECT userId, COUNT(*) as count FROM tracking_events GROUP BY userId ORDER BY count DESC LIMIT 10', 
                [], 
                (err, rows) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(rows);
                }
            );
        });
    }

    /**
     * 获取事件统计
     */
    async getEventStatistics() {
        return new Promise((resolve, reject) => {
            this.database.db.all(
                'SELECT event, COUNT(*) as count FROM tracking_events GROUP BY event ORDER BY count DESC LIMIT 10', 
                [], 
                (err, rows) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(rows);
                }
            );
        });
    }

    /**
     * 获取时间范围
     */
    async getTimeRange() {
        return new Promise((resolve, reject) => {
            this.database.db.get(
                'SELECT MIN(created_at) as earliest, MAX(created_at) as latest FROM tracking_events', 
                [], 
                (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(row);
                }
            );
        });
    }

    /**
     * 主程序循环
     */
    async run() {
        try {
            console.log('🚀 启动打点数据清理工具...');
            
            // 检查数据库文件是否存在
            const dbPath = path.join(__dirname, 'tracking.db');
            if (!fs.existsSync(dbPath)) {
                console.log('📊 数据库文件不存在，无需清理');
                this.rl.close();
                return;
            }

            await this.initDatabase();

            while (true) {
                this.showMenu();
                const choice = await this.getUserChoice();

                switch (choice) {
                    case 1:
                        await this.clearAllData();
                        break;
                    case 2:
                        await this.clearDataByUserId();
                        break;
                    case 3:
                        await this.clearDataByTimeRange();
                        break;
                    case 4:
                        await this.deleteDatabaseFile();
                        break;
                    case 5:
                        await this.showDataStatistics();
                        break;
                    case 6:
                        console.log('👋 再见！');
                        await this.cleanup();
                        return;
                    default:
                        console.log('❌ 无效选择，请重新输入');
                        break;
                }

                // 暂停一下让用户看到结果
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
        } catch (error) {
            console.error('❌ 程序运行出错:', error.message);
            await this.cleanup();
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        if (this.database) {
            await this.database.close();
        }
        this.rl.close();
    }
}

// 主程序入口
if (require.main === module) {
    const cleaner = new DataCleaner();
    
    // 处理程序退出信号
    process.on('SIGINT', async () => {
        console.log('\n\n👋 程序被中断，正在清理资源...');
        await cleaner.cleanup();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('\n\n👋 程序被终止，正在清理资源...');
        await cleaner.cleanup();
        process.exit(0);
    });

    cleaner.run().catch(console.error);
}

module.exports = DataCleaner;
