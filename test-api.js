/**
 * Test script for the DPA API endpoint
 * This script tests various scenarios including valid requests, invalid requests, and error handling
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

/**
 * Make an HTTP request
 * @param {string} method - HTTP method
 * @param {string} path - Request path
 * @param {Object} data - Request body data
 * @returns {Promise<Object>} - Response data
 */
function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            
            res.on('data', (chunk) => {
                body += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: JSON.parse(body)
                    };
                    resolve(response);
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

/**
 * Test cases
 */
const testCases = [
    {
        name: 'Health Check',
        method: 'GET',
        path: '/health',
        data: null,
        expectedStatus: 200
    },
    {
        name: 'Valid DPA Request',
        method: 'POST',
        path: '/dpa',
        data: {
            "userId": 34189126,
            "univCode": "RC00116",
            "location": "http://192.168.1.76:3800/pd-dpa-overall",
            "seqNo": 28,
            "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
            "event": "export_data",
            "args": {"currVerNo": 202505}
        },
        expectedStatus: 201
    },
    {
        name: 'Missing Required Field (userId)',
        method: 'POST',
        path: '/dpa',
        data: {
            "univCode": "RC00116",
            "location": "http://192.168.1.76:3800/pd-dpa-overall",
            "seqNo": 28,
            "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
            "event": "export_data",
            "args": {"currVerNo": 202505}
        },
        expectedStatus: 400
    },
    {
        name: 'Valid DPA Request without optional fields',
        method: 'POST',
        path: '/dpa',
        data: {
            "userId": 12345,
            "univCode": "RC00117",
            "location": "http://192.168.1.76:3800/pd-dpa-minimal",
            "seqNo": 1,
            "session": "550e8400-e29b-41d4-a716-446655440001"
        },
        expectedStatus: 201
    },
    {
        name: 'Invalid Data Type (userId as string)',
        method: 'POST',
        path: '/dpa',
        data: {
            "userId": "34189126",
            "univCode": "RC00116",
            "location": "http://192.168.1.76:3800/pd-dpa-overall",
            "seqNo": 28,
            "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
            "event": "export_data",
            "args": {"currVerNo": 202505}
        },
        expectedStatus: 400
    },
    {
        name: 'Invalid URL Format',
        method: 'POST',
        path: '/dpa',
        data: {
            "userId": 34189126,
            "univCode": "RC00116",
            "location": "invalid-url",
            "seqNo": 28,
            "session": "fbf89896-eeb5-4586-921b-b2a636b826e9",
            "event": "export_data",
            "args": {"currVerNo": 202505}
        },
        expectedStatus: 400
    },
    {
        name: 'Invalid UUID Format',
        method: 'POST',
        path: '/dpa',
        data: {
            "userId": 34189126,
            "univCode": "RC00116",
            "location": "http://192.168.1.76:3800/pd-dpa-overall",
            "seqNo": 28,
            "session": "invalid-uuid",
            "event": "export_data",
            "args": {"currVerNo": 202505}
        },
        expectedStatus: 400
    },
    {
        name: 'Get All DPA Events',
        method: 'GET',
        path: '/dpa',
        data: null,
        expectedStatus: 200
    }
];

/**
 * Run all test cases
 */
async function runTests() {
    console.log('🧪 Starting API Tests...\n');
    
    let passed = 0;
    let failed = 0;

    for (const testCase of testCases) {
        try {
            console.log(`Testing: ${testCase.name}`);
            
            const response = await makeRequest(testCase.method, testCase.path, testCase.data);
            
            if (response.statusCode === testCase.expectedStatus) {
                console.log(`✅ PASS - Status: ${response.statusCode}`);
                if (response.body && typeof response.body === 'object') {
                    console.log(`   Response: ${JSON.stringify(response.body, null, 2)}`);
                }
                passed++;
            } else {
                console.log(`❌ FAIL - Expected: ${testCase.expectedStatus}, Got: ${response.statusCode}`);
                console.log(`   Response: ${JSON.stringify(response.body, null, 2)}`);
                failed++;
            }
            
        } catch (error) {
            console.log(`❌ ERROR - ${error.message}`);
            failed++;
        }
        
        console.log(''); // Empty line for readability
    }

    console.log('📊 Test Results:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Total: ${passed + failed}`);
    
    if (failed === 0) {
        console.log('\n🎉 All tests passed!');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the server logs.');
    }
}

// Check if server is running before starting tests
async function checkServerHealth() {
    try {
        const response = await makeRequest('GET', '/health');
        if (response.statusCode === 200) {
            console.log('✅ Server is running and healthy\n');
            return true;
        } else {
            console.log('❌ Server health check failed');
            return false;
        }
    } catch (error) {
        console.log('❌ Cannot connect to server. Make sure the server is running on http://localhost:3000');
        console.log('   Run: npm start');
        return false;
    }
}

// Main execution
async function main() {
    console.log('🚀 DPA API Test Suite');
    console.log('===================\n');
    
    const isHealthy = await checkServerHealth();
    if (isHealthy) {
        await runTests();
    }
}

if (require.main === module) {
    main();
}

module.exports = { makeRequest, runTests };
