<template>
  <div class="example-container">
    <DynamicTable
      :data="tableData"
      :columns="tableColumns"
      :title="tableTitle"
      :container-width="700"
      :container-height="1000"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DynamicTable from './DynamicTable.vue'

// 模拟您图片中的数据结构
const tableTitle = ref('表4.2 上海大学Nature和Science论文清单')

const tableColumns = ref([
  { key: 'sequence', label: '序号', width: 50 },
  { key: 'title', label: '论文名称', width: 200 },
  { key: 'journal', label: '期刊', width: 80 },
  { key: 'year', label: '发表年份', width: 80 },
  { key: 'volume', label: '卷/期', width: 80 },
  { key: 'impact', label: '影响因子', width: 70 },
  { key: 'partition', label: '分区', width: 50 },
  { key: 'authors', label: '作者', width: 90 }
])

// 模拟数据（基于您图片中的内容）
const tableData = ref([
  {
    sequence: 1,
    title: 'Fabrication of red-emitting carbon dots with enhanced fluorescence for optical anti-counterfeiting and information encryption',
    journal: 'NATURE',
    year: 2024,
    volume: '631/8019',
    impact: 1,
    partition: 1,
    authors: '张三等'
  },
  {
    sequence: 2,
    title: 'Nature of metal-support interaction for metal nanoparticles on oxide supports',
    journal: 'SCIENCE',
    year: 2024,
    volume: '384/6724',
    impact: 0.25,
    partition: 0.25,
    authors: '李四等'
  },
  {
    sequence: 3,
    title: 'Two-dimensional chiral perovskites with large spin-Hall angle and millimeter-spin diffusion length',
    journal: 'SCIENCE',
    year: 2024,
    volume: '385/6704',
    impact: 0.10,
    partition: 0.10,
    authors: '王五等'
  },
  {
    sequence: 4,
    title: 'Superconductivity under pressure in chromium-based kagome metals',
    journal: 'NATURE',
    year: 2024,
    volume: '632/8027',
    impact: 0.10,
    partition: 0.10,
    authors: '赵六等'
  },
  {
    sequence: 5,
    title: 'Telomeric mixture coordination enables efficient lead-free perovskite LEDs',
    journal: 'NATURE',
    year: 2023,
    volume: '622/7983',
    impact: 0.50,
    partition: 0.50,
    authors: '孙七等'
  },
  {
    sequence: 6,
    title: 'Cysteine carboxylmethylation generates microswitches for protein folding and autoimmunity',
    journal: 'SCIENCE',
    year: 2023,
    volume: '379/6437',
    impact: 0.50,
    partition: 0.50,
    authors: '周八等'
  },
  {
    sequence: 7,
    title: 'Pair density wave state in a monolayer high-Tc cuprate superconductor',
    journal: 'NATURE',
    year: 2023,
    volume: '618/7967',
    impact: 0.10,
    partition: 0.10,
    authors: '吴九等'
  },
  {
    sequence: 8,
    title: 'Electrically switchable van der Waals magnon valves',
    journal: 'NATURE',
    year: 2023,
    volume: '622/7982',
    impact: 0.10,
    partition: 0.10,
    authors: '郑十等'
  },
  {
    sequence: 9,
    title: 'Heterodimensional superlattice with in-plane anomalous Hall effect',
    journal: 'NATURE',
    year: 2022,
    volume: '609/7925',
    impact: 0.10,
    partition: 0.10,
    authors: '陈十一等'
  },
  {
    sequence: 10,
    title: 'A highly distorted ultrafast-rotating stellar core in a massive star',
    journal: 'NATURE',
    year: 2022,
    volume: '602/7896',
    impact: 0.10,
    partition: 0.10,
    authors: '林十二等'
  },
  {
    sequence: 11,
    title: 'Hierarchical crack buffering triples ductility in eutectic herringbone high-entropy alloys',
    journal: 'SCIENCE',
    year: 2021,
    volume: '373/6557',
    impact: 1,
    partition: 1,
    authors: '黄十三等'
  },
  {
    sequence: 12,
    title: 'Structural insights into Ube1-mediated E1-E2 conjugation in ubiquitin signaling',
    journal: 'NATURE',
    year: 2021,
    volume: '600/7888',
    impact: 0.25,
    partition: 0.25,
    authors: '杨十四等'
  },
  {
    sequence: 13,
    title: 'Morphological diversity of neurons in molecularly defined cell types',
    journal: 'NATURE',
    year: 2021,
    volume: '598/7879',
    impact: 0.10,
    partition: 0.10,
    authors: '刘十五等'
  },
  {
    sequence: 14,
    title: 'A multimodal cell census and atlas of the mammalian primary motor cortex',
    journal: 'NATURE',
    year: 2021,
    volume: '598/7879',
    impact: 0.10,
    partition: 0.10,
    authors: '何十六等'
  },
  {
    sequence: 15,
    title: 'The anatomy of the mouse primary motor cortex',
    journal: 'NATURE',
    year: 2021,
    volume: '598/7879',
    impact: 0.10,
    partition: 0.10,
    authors: '罗十七等'
  }
])

// 可以添加更多数据来测试分页功能
const addMoreData = () => {
  const additionalData = []
  for (let i = 16; i <= 50; i++) {
    additionalData.push({
      sequence: i,
      title: `这是第${i}篇论文的标题，内容可能会很长，需要测试动态高度计算功能，确保在固定页面尺寸内正确显示`,
      journal: i % 2 === 0 ? 'NATURE' : 'SCIENCE',
      year: 2020 + (i % 5),
      volume: `${600 + i}/${7000 + i}`,
      impact: (Math.random() * 0.9 + 0.1).toFixed(2),
      partition: (Math.random() * 0.9 + 0.1).toFixed(2),
      authors: `作者${i}等`
    })
  }
  tableData.value.push(...additionalData)
}

// 自动添加更多数据用于测试
addMoreData()
</script>

<style scoped>
.example-container {
  min-height: 100vh;
  background-color: #f0f0f0;
}
</style>
