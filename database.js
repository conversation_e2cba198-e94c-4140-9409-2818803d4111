const sqlite3 = require('sqlite3').verbose();
const path = require('path');

/**
 * 获取服务器本地时间字符串
 * @returns {string} 本地时间字符串 (YYYY-MM-DD HH:MM:SS)
 */
function getLocalTime() {
    const now = new Date();
    // 获取本地时间字符串
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

class Database {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, 'tracking.db');
    }

    /**
     * Initialize the database connection and create tables if they don't exist
     */
    async init() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err.message);
                    reject(err);
                    return;
                }
                console.log('Connected to SQLite database');
                this.createTables()
                    .then(() => resolve())
                    .catch(reject);
            });
        });
    }

    /**
     * Create the tracking_events table if it doesn't exist
     */
    async createTables() {
        return new Promise((resolve, reject) => {
            const createTableSQL = `
                CREATE TABLE IF NOT EXISTS tracking_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    userId INTEGER NOT NULL,
                    univCode TEXT NOT NULL,
                    location TEXT NOT NULL,
                    seqNo INTEGER NOT NULL,
                    session TEXT NOT NULL,
                    event TEXT,
                    args TEXT,
                    created_at DATETIME DEFAULT (datetime('now', 'localtime'))
                )
            `;

            this.db.run(createTableSQL, (err) => {
                if (err) {
                    console.error('Error creating table:', err.message);
                    reject(err);
                    return;
                }
                console.log('Tracking events table created or already exists');
                resolve();
            });
        });
    }

    /**
     * Insert a new tracking event into the database
     * @param {Object} eventData - The tracking event data
     * @returns {Promise<number>} - The ID of the inserted record
     */
    async insertTrackingEvent(eventData) {
        return new Promise((resolve, reject) => {
            const { userId, univCode, location, seqNo, session, event, args } = eventData;

            // Convert args object to JSON string if it exists
            const argsString = args !== undefined ?
                (typeof args === 'object' ? JSON.stringify(args) : args) : null;

            // Handle optional event field
            const eventValue = event !== undefined ? event : null;

            const insertSQL = `
                INSERT INTO tracking_events (userId, univCode, location, seqNo, session, event, args, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))
            `;

            this.db.run(insertSQL, [userId, univCode, location, seqNo, session, eventValue, argsString], function(err) {
                if (err) {
                    console.error('Error inserting tracking event:', err.message);
                    reject(err);
                    return;
                }
                console.log(`Tracking event inserted with ID: ${this.lastID}`);
                resolve(this.lastID);
            });
        });
    }

    /**
     * Get all tracking events (for testing purposes)
     * @returns {Promise<Array>} - Array of tracking events
     */
    async getAllTrackingEvents() {
        return new Promise((resolve, reject) => {
            const selectSQL = 'SELECT * FROM tracking_events ORDER BY created_at DESC';
            
            this.db.all(selectSQL, [], (err, rows) => {
                if (err) {
                    console.error('Error fetching tracking events:', err.message);
                    reject(err);
                    return;
                }
                resolve(rows);
            });
        });
    }

    /**
     * Close the database connection
     */
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                        reject(err);
                        return;
                    }
                    console.log('Database connection closed');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = Database;
module.exports.getLocalTime = getLocalTime;
