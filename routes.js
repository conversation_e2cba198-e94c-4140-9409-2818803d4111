const express = require('express');
const { validateDpaRequest } = require('./validation');
const { getLocalTime } = require('./database');

/**
 * Create and configure routes for the application
 * @param {Database} database - Database instance
 * @returns {express.Router} - Configured router
 */
function createRoutes(database) {
    const router = express.Router();

    /**
     * POST /dpa - Data Processing Analytics endpoint
     * Accepts tracking/analytics data and stores it in the database
     */
    router.post('/dpa', validateDpaRequest, async (req, res) => {
        try {
            const { userId, univCode, location, seqNo, session, event, args } = req.body;

            // Log the incoming request for debugging
            console.log('Received DPA request:', {
                userId,
                univCode,
                location,
                seqNo,
                session,
                event,
                args: JSON.stringify(args)
            });

            // Insert the tracking event into the database
            const insertedId = await database.insertTrackingEvent({
                userId,
                univCode,
                location,
                seqNo,
                session,
                event,
                args
            });

            // Return success response
            res.status(201).json({
                success: true,
                code: 200,
                message: 'Tracking event recorded successfully',
                data: {
                    id: insertedId,
                    userId,
                    univCode,
                    location,
                    seqNo,
                    session,
                    event: event || null,
                    args: args || null,
                    timestamp: getLocalTime()
                }
            });

        } catch (error) {
            console.error('Error processing DPA request:', error);
            
            // Return error response
            res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: 'Failed to process tracking event'
            });
        }
    });

    /**
     * GET /dpa - Get all tracking events (for testing/debugging purposes)
     * This endpoint can be used to verify that data is being stored correctly
     */
    router.get('/dpa', async (req, res) => {
        try {
            const events = await database.getAllTrackingEvents();
            
            res.status(200).json({
                success: true,
                message: 'Tracking events retrieved successfully',
                count: events.length,
                data: events
            });

        } catch (error) {
            console.error('Error retrieving tracking events:', error);
            
            res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: 'Failed to retrieve tracking events'
            });
        }
    });

    /**
     * GET /health - Health check endpoint
     * Simple endpoint to check if the server is running
     */
    router.get('/health', (req, res) => {
        res.status(200).json({
            success: true,
            message: 'Server is running',
            timestamp: getLocalTime()
        });
    });

    return router;
}

module.exports = createRoutes;
