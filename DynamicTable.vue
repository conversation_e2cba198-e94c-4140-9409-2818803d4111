<template>
  <div class="pages-container">
    <!-- 渲染所有页面 -->
    <div
      v-for="(pageData, pageIndex) in allPages"
      :key="pageIndex"
      class="page"
      :style="pageStyle"
    >
      <!-- 表头 -->
      <div class="table-header">
        <h2 class="title">{{ title }}{{ allPages.length > 1 ? ` (第${pageIndex + 1}页)` : '' }}</h2>
        <div class="header-row">
          <div
            v-for="(column, index) in columns"
            :key="index"
            class="header-cell"
            :style="{ width: column.width + 'px' }"
          >
            {{ column.label }}
          </div>
        </div>
      </div>

      <!-- 表格内容区域 -->
      <div class="table-content">
        <div
          v-for="(row, rowIndex) in pageData"
          :key="row.sequence || rowIndex"
          class="table-row"
          :style="{ height: getRowHeight(pageIndex, rowIndex) + 'px' }"
        >
          <div
            v-for="(column, colIndex) in columns"
            :key="colIndex"
            class="table-cell"
            :style="{ width: column.width + 'px' }"
          >
            <div class="cell-content">
              {{ row[column.key] }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: '数据表格'
  },
  containerWidth: {
    type: Number,
    default: 700
  },
  containerHeight: {
    type: Number,
    default: 1000
  }
})

// 响应式数据
const rowHeights = ref([])
const allPages = ref([])

// 计算属性
const pageStyle = computed(() => ({
  width: props.containerWidth + 'px',
  height: props.containerHeight + 'px'
}))

const availableContentHeight = computed(() => {
  // 减去标题、表头的高度，不需要分页控制
  return props.containerHeight - 80 // 预留80px给标题、表头
})

// 获取指定行的高度
const getRowHeight = (pageIndex, rowIndex) => {
  const globalRowIndex = allPages.value.slice(0, pageIndex)
    .reduce((sum, page) => sum + page.length, 0) + rowIndex
  return rowHeights.value[globalRowIndex] || 40
}

// 创建文本测量画布（复用以提高性能）
let measureCanvas = null
const getMeasureCanvas = () => {
  if (!measureCanvas) {
    measureCanvas = document.createElement('canvas')
  }
  return measureCanvas
}

// 精确的文本测量工具
const measureTextHeight = (text, width, fontSize = 12, fontFamily = 'Arial, sans-serif') => {
  if (!text || width <= 0) return 40 // 最小高度

  const canvas = getMeasureCanvas()
  const context = canvas.getContext('2d')
  context.font = `${fontSize}px ${fontFamily}`

  const padding = 20 // 左右padding总和
  const availableWidth = width - padding
  const lineHeight = fontSize * 1.4 // 行高倍数

  // 按字符分割，适合中英文混合
  const chars = Array.from(text)
  const lines = []
  let currentLine = ''

  for (const char of chars) {
    const testLine = currentLine + char
    const metrics = context.measureText(testLine)

    if (metrics.width > availableWidth && currentLine.length > 0) {
      lines.push(currentLine)
      currentLine = char
    } else {
      currentLine = testLine
    }
  }

  if (currentLine) {
    lines.push(currentLine)
  }

  const totalHeight = Math.max(lines.length * lineHeight + 16, 40) // 最小40px
  return Math.ceil(totalHeight)
}

// 计算所有行的高度并分页
const calculateRowHeights = async () => {
  await nextTick()

  const heights = []

  // 计算每行高度
  for (let i = 0; i < props.data.length; i++) {
    const row = props.data[i]
    let maxHeight = 40 // 最小行高

    props.columns.forEach(column => {
      const text = String(row[column.key] || '')
      const cellHeight = measureTextHeight(text, column.width, 12)
      maxHeight = Math.max(maxHeight, cellHeight)
    })

    heights.push(maxHeight)
  }

  rowHeights.value = heights
  splitDataIntoPages()
}

// 将数据分割成多个页面
const splitDataIntoPages = () => {
  const pages = []
  let currentPageData = []
  let currentPageHeight = 0
  const maxPageHeight = availableContentHeight.value

  for (let i = 0; i < props.data.length; i++) {
    const rowHeight = rowHeights.value[i]

    // 检查添加这一行是否会超出页面高度
    if (currentPageHeight + rowHeight > maxPageHeight && currentPageData.length > 0) {
      // 当前页已满，开始新页
      pages.push([...currentPageData])
      currentPageData = [props.data[i]]
      currentPageHeight = rowHeight
    } else {
      // 添加到当前页
      currentPageData.push(props.data[i])
      currentPageHeight += rowHeight
    }
  }

  // 添加最后一页
  if (currentPageData.length > 0) {
    pages.push(currentPageData)
  }

  allPages.value = pages
}



// 监听数据变化
watch(() => props.data, () => {
  if (props.data.length > 0) {
    calculateRowHeights()
  }
}, { immediate: true })

watch(() => props.columns, () => {
  if (props.data.length > 0) {
    calculateRowHeights()
  }
}, { immediate: true })

onMounted(() => {
  if (props.data.length > 0) {
    calculateRowHeights()
  }
})
</script>

<style scoped>
.pages-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f0f0f0;
}

.page {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  background: #f5f5f5;
  border-bottom: 2px solid #ddd;
}

.title {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.header-row {
  display: flex;
  background: #e8e8e8;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
}

.header-cell {
  padding: 8px 10px;
  border-right: 1px solid #ccc;
  text-align: center;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-cell:last-child {
  border-right: none;
}

.table-content {
  flex: 1;
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  align-items: stretch;
}

.table-row:hover {
  background-color: #f9f9f9;
}

.table-cell {
  border-right: 1px solid #eee;
  display: flex;
  align-items: center;
  padding: 0;
}

.table-cell:last-child {
  border-right: none;
}

.cell-content {
  padding: 8px 10px;
  font-size: 12px;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
