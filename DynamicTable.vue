<template>
  <div class="dynamic-table-container" :style="containerStyle">
    <!-- 表头 -->
    <div class="table-header">
      <h2 class="title">{{ title }}</h2>
      <div class="header-row">
        <div 
          v-for="(column, index) in columns" 
          :key="index"
          class="header-cell"
          :style="{ width: column.width + 'px' }"
        >
          {{ column.label }}
        </div>
      </div>
    </div>

    <!-- 表格内容区域 -->
    <div class="table-content" ref="contentRef">
      <div 
        v-for="(row, rowIndex) in currentPageData" 
        :key="rowIndex"
        class="table-row"
        :style="{ height: rowHeights[currentPage * pageSize + rowIndex] + 'px' }"
      >
        <div 
          v-for="(column, colIndex) in columns"
          :key="colIndex"
          class="table-cell"
          :style="{ width: column.width + 'px' }"
        >
          <div class="cell-content" :ref="el => setCellRef(el, rowIndex, colIndex)">
            {{ row[column.key] }}
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控制 -->
    <div class="pagination">
      <button 
        @click="prevPage" 
        :disabled="currentPage === 0"
        class="page-btn"
      >
        上一页
      </button>
      <span class="page-info">
        {{ currentPage + 1 }} / {{ totalPages }}
      </span>
      <button 
        @click="nextPage" 
        :disabled="currentPage === totalPages - 1"
        class="page-btn"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: '数据表格'
  },
  containerWidth: {
    type: Number,
    default: 700
  },
  containerHeight: {
    type: Number,
    default: 1000
  }
})

// 响应式数据
const contentRef = ref(null)
const currentPage = ref(0)
const rowHeights = ref([])
const cellRefs = ref(new Map())
const pageSize = ref(10) // 初始每页显示数量，会动态调整

// 计算属性
const containerStyle = computed(() => ({
  width: props.containerWidth + 'px',
  height: props.containerHeight + 'px'
}))

const availableContentHeight = computed(() => {
  // 减去标题、表头、分页控制的高度
  return props.containerHeight - 120 // 预留120px给标题、表头、分页
})

const totalPages = computed(() => {
  return Math.ceil(props.data.length / pageSize.value)
})

const currentPageData = computed(() => {
  const start = currentPage.value * pageSize.value
  const end = start + pageSize.value
  return props.data.slice(start, end)
})

// 创建文本测量画布（复用以提高性能）
let measureCanvas = null
const getMeasureCanvas = () => {
  if (!measureCanvas) {
    measureCanvas = document.createElement('canvas')
  }
  return measureCanvas
}

// 精确的文本测量工具
const measureTextHeight = (text, width, fontSize = 12, fontFamily = 'Arial, sans-serif') => {
  if (!text || width <= 0) return 40 // 最小高度

  const canvas = getMeasureCanvas()
  const context = canvas.getContext('2d')
  context.font = `${fontSize}px ${fontFamily}`

  const padding = 20 // 左右padding总和
  const availableWidth = width - padding
  const lineHeight = fontSize * 1.4 // 行高倍数

  // 按字符分割，适合中英文混合
  const chars = Array.from(text)
  const lines = []
  let currentLine = ''

  for (const char of chars) {
    const testLine = currentLine + char
    const metrics = context.measureText(testLine)

    if (metrics.width > availableWidth && currentLine.length > 0) {
      lines.push(currentLine)
      currentLine = char
    } else {
      currentLine = testLine
    }
  }

  if (currentLine) {
    lines.push(currentLine)
  }

  const totalHeight = Math.max(lines.length * lineHeight + 16, 40) // 最小40px
  return Math.ceil(totalHeight)
}

// 计算所有行的高度（优化版本）
const calculateRowHeights = async () => {
  await nextTick()

  const heights = []
  const batchSize = 50 // 批量处理以避免阻塞UI

  const processBatch = (startIndex) => {
    const endIndex = Math.min(startIndex + batchSize, props.data.length)

    for (let i = startIndex; i < endIndex; i++) {
      const row = props.data[i]
      let maxHeight = 40 // 最小行高

      props.columns.forEach(column => {
        const text = String(row[column.key] || '')
        const cellHeight = measureTextHeight(text, column.width, 12)
        maxHeight = Math.max(maxHeight, cellHeight)
      })

      heights.push(maxHeight)
    }

    // 如果还有更多数据，异步处理下一批
    if (endIndex < props.data.length) {
      setTimeout(() => processBatch(endIndex), 0)
    } else {
      // 所有数据处理完成
      rowHeights.value = heights
      calculateOptimalPageSize()
    }
  }

  // 开始处理第一批
  processBatch(0)
}

// 动态计算最优每页显示数量（改进版本）
const calculateOptimalPageSize = () => {
  if (rowHeights.value.length === 0) return

  let totalHeight = 0
  let optimalSize = 0
  const maxHeight = availableContentHeight.value

  for (let i = 0; i < rowHeights.value.length; i++) {
    totalHeight += rowHeights.value[i]

    if (totalHeight <= maxHeight) {
      optimalSize = i + 1
    } else {
      break
    }
  }

  // 确保至少显示一行，最多不超过数据总量
  const newPageSize = Math.max(1, Math.min(optimalSize, props.data.length))

  // 只有当新的页面大小与当前不同时才更新
  if (newPageSize !== pageSize.value) {
    pageSize.value = newPageSize

    // 调整当前页码，确保不超出范围
    const maxPage = Math.ceil(props.data.length / pageSize.value) - 1
    if (currentPage.value > maxPage) {
      currentPage.value = Math.max(0, maxPage)
    }
  }
}

// 设置单元格引用
const setCellRef = (el, rowIndex, colIndex) => {
  if (el) {
    const key = `${rowIndex}-${colIndex}`
    cellRefs.value.set(key, el)
  }
}

// 分页控制
const nextPage = () => {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--
  }
}

// 监听数据变化
watch(() => props.data, () => {
  calculateRowHeights()
}, { immediate: true })

watch(() => props.columns, () => {
  calculateRowHeights()
}, { immediate: true })

onMounted(() => {
  calculateRowHeights()
})
</script>

<style scoped>
.dynamic-table-container {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  display: flex;
  flex-direction: column;
}

.table-header {
  background: #f5f5f5;
  border-bottom: 2px solid #ddd;
}

.title {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.header-row {
  display: flex;
  background: #e8e8e8;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
}

.header-cell {
  padding: 8px 10px;
  border-right: 1px solid #ccc;
  text-align: center;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-cell:last-child {
  border-right: none;
}

.table-content {
  flex: 1;
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
  align-items: stretch;
}

.table-row:hover {
  background-color: #f9f9f9;
}

.table-cell {
  border-right: 1px solid #eee;
  display: flex;
  align-items: center;
  padding: 0;
}

.table-cell:last-child {
  border-right: none;
}

.cell-content {
  padding: 8px 10px;
  font-size: 12px;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
  gap: 15px;
}

.page-btn {
  padding: 5px 15px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.page-btn:hover:not(:disabled) {
  background: #e8e8e8;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #666;
}
</style>
