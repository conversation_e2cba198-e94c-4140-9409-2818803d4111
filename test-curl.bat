@echo off
echo Testing DPA API with curl...
echo.

echo 1. Health Check:
curl -X GET http://localhost:3000/health
echo.
echo.

echo 2. Valid DPA Request:
curl -X POST http://localhost:3000/dpa ^
  -H "Content-Type: application/json" ^
  -d "{\"userId\": 12345, \"univCode\": \"TEST001\", \"location\": \"http://example.com/test\", \"seqNo\": 1, \"session\": \"550e8400-e29b-41d4-a716-446655440000\", \"event\": \"page_view\", \"args\": {\"page\": \"dashboard\", \"version\": \"1.0\"}}"
echo.
echo.

echo 3. Get All Events:
curl -X GET http://localhost:3000/dpa
echo.
echo.

echo 4. Invalid Request (missing userId):
curl -X POST http://localhost:3000/dpa ^
  -H "Content-Type: application/json" ^
  -d "{\"univCode\": \"TEST001\", \"location\": \"http://example.com/test\", \"seqNo\": 1, \"session\": \"550e8400-e29b-41d4-a716-446655440000\", \"event\": \"page_view\", \"args\": {\"page\": \"dashboard\"}}"
echo.
echo.

echo Tests completed!
pause
