/**
 * Validation middleware for the /dpa endpoint
 */

/**
 * Validate the required fields in the request body
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateDpaRequest(req, res, next) {
    const { body } = req;
    
    // Check if body exists
    if (!body || typeof body !== 'object') {
        return res.status(400).json({
            error: 'Invalid request body',
            message: 'Request body must be a valid JSON object'
        });
    }

    // Define required fields and their expected types
    const requiredFields = {
        userId: 'number',
        univCode: 'string',
        location: 'string',
        seqNo: 'number',
        session: 'string'
    };

    // Define optional fields and their expected types
    const optionalFields = {
        event: 'string',
        args: 'object'
    };

    // Check for missing required fields and validate types
    const missingFields = [];
    const invalidTypes = [];

    // Validate required fields
    for (const [field, expectedType] of Object.entries(requiredFields)) {
        if (!(field in body)) {
            missingFields.push(field);
        } else {
            const actualType = typeof body[field];
            if (actualType !== expectedType) {
                invalidTypes.push({
                    field,
                    expected: expectedType,
                    actual: actualType
                });
            }
        }
    }

    // Validate optional fields (only if they exist)
    for (const [field, expectedType] of Object.entries(optionalFields)) {
        if (field in body) {
            const actualType = typeof body[field];
            if (actualType !== expectedType) {
                invalidTypes.push({
                    field,
                    expected: expectedType,
                    actual: actualType
                });
            }
        }
    }

    // Return validation errors if any
    if (missingFields.length > 0 || invalidTypes.length > 0) {
        const errorResponse = {
            error: 'Validation failed',
            details: {}
        };

        if (missingFields.length > 0) {
            errorResponse.details.missingFields = missingFields;
        }

        if (invalidTypes.length > 0) {
            errorResponse.details.invalidTypes = invalidTypes;
        }

        return res.status(400).json(errorResponse);
    }

    // Additional validation for specific fields
    if (body.userId <= 0) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'userId must be a positive number'
        });
    }

    if (body.seqNo < 0) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'seqNo must be a non-negative number'
        });
    }

    if (!body.univCode.trim()) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'univCode cannot be empty'
        });
    }

    if (!body.location.trim()) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'location cannot be empty'
        });
    }

    if (!body.session.trim()) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'session cannot be empty'
        });
    }

    // Validate event field only if it exists
    if (body.event !== undefined && !body.event.trim()) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'event cannot be empty'
        });
    }

    // Validate URL format for location (basic validation)
    try {
        new URL(body.location);
    } catch (error) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'location must be a valid URL'
        });
    }

    // Validate UUID format for session (basic validation)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(body.session)) {
        return res.status(400).json({
            error: 'Validation failed',
            message: 'session must be a valid UUID format'
        });
    }

    // If all validations pass, continue to the next middleware
    next();
}

module.exports = {
    validateDpaRequest
};
