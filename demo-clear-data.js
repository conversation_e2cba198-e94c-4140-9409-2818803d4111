/**
 * 数据清理工具演示脚本
 * 展示如何使用 DataCleaner 类进行编程式数据清理
 */

const DataCleaner = require('./clear-data');

async function demo() {
    const cleaner = new DataCleaner();
    
    try {
        console.log('🚀 数据清理工具演示');
        console.log('==================\n');
        
        // 初始化数据库
        await cleaner.initDatabase();
        
        // 显示当前数据统计
        console.log('📊 当前数据统计:');
        await cleaner.showDataStatistics();
        
        console.log('\n' + '='.repeat(50));
        console.log('演示完成！');
        console.log('💡 要进行实际的数据清理操作，请运行: node clear-data.js');
        
    } catch (error) {
        console.error('❌ 演示过程中出错:', error.message);
    } finally {
        await cleaner.cleanup();
    }
}

// 运行演示
if (require.main === module) {
    demo().catch(console.error);
}

module.exports = demo;
